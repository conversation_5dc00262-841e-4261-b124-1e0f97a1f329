<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>事件委托测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .container {
            border: 2px solid #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
        }
        .btn {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .btn:hover {
            background-color: #0056b3;
        }
        .nested {
            background-color: #f8f9fa;
            padding: 15px;
            margin: 10px 0;
            border: 1px solid #dee2e6;
            border-radius: 4px;
        }
        .log {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            margin: 10px 0;
            border-radius: 4px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
        .controls {
            margin: 20px 0;
        }
        .controls button {
            background-color: #28a745;
            color: white;
            border: none;
            padding: 8px 16px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <h1>事件委托功能测试</h1>
    
    <div class="controls">
        <button onclick="setupBasicDelegate()">设置基础委托</button>
        <button onclick="setupAdvancedDelegate()">设置高级委托</button>
        <button onclick="removeAdvancedDelegate()">移除高级委托</button>
        <button onclick="addNewButton()">动态添加按钮</button>
        <button onclick="clearLog()">清空日志</button>
    </div>

    <div id="container1" class="container">
        <h3>容器1 - 基础委托测试</h3>
        <button class="btn" data-id="1">按钮 1</button>
        <button class="btn" data-id="2">按钮 2</button>
        <div class="nested">
            <p>嵌套内容</p>
            <button class="btn" data-id="3">嵌套按钮 3</button>
            <span>
                <button class="btn" data-id="4">深度嵌套按钮 4</button>
            </span>
        </div>
    </div>

    <div id="container2" class="container">
        <h3>容器2 - 高级委托测试</h3>
        <button class="btn" data-id="5">按钮 5</button>
        <button class="btn" data-id="6">按钮 6</button>
        <div class="nested">
            <button class="btn" data-id="7">嵌套按钮 7</button>
        </div>
    </div>

    <div class="log" id="log">
        <div>事件日志将显示在这里...</div>
    </div>

    <script type="module">
        // 模拟DOM工具库的委托函数
        function delegate(container, selector, type, listener) {
            const delegateHandler = (event) => {
                if (!event.target || !(event.target instanceof HTMLElement)) {
                    return;
                }

                const target = event.target.closest(selector);
                
                if (target && container.contains(target)) {
                    listener(event, target);
                }
            };

            container.addEventListener(type, delegateHandler);
        }

        // 高级委托函数
        const delegateMap = new WeakMap();

        function delegateAdvanced(container, selector, type, listener) {
            const delegateHandler = (event) => {
                if (!event.target || !(event.target instanceof HTMLElement)) {
                    return;
                }

                const target = event.target.closest(selector);
                
                if (target && container.contains(target)) {
                    listener(event, target);
                }
            };

            if (!delegateMap.has(container)) {
                delegateMap.set(container, new Map());
            }
            
            const containerMap = delegateMap.get(container);
            const key = `${type}:${selector}`;
            
            if (containerMap.has(key)) {
                const oldHandler = containerMap.get(key);
                container.removeEventListener(type, oldHandler);
            }
            
            containerMap.set(key, delegateHandler);
            container.addEventListener(type, delegateHandler);

            return () => {
                container.removeEventListener(type, delegateHandler);
                containerMap.delete(key);
                if (containerMap.size === 0) {
                    delegateMap.delete(container);
                }
            };
        }

        // 日志函数
        function log(message) {
            const logElement = document.getElementById('log');
            const time = new Date().toLocaleTimeString();
            const logEntry = document.createElement('div');
            logEntry.textContent = `[${time}] ${message}`;
            logElement.appendChild(logEntry);
            logElement.scrollTop = logElement.scrollHeight;
        }

        // 全局变量存储移除函数
        let removeAdvancedDelegateFunc = null;

        // 设置基础委托
        window.setupBasicDelegate = function() {
            const container1 = document.getElementById('container1');
            delegate(container1, '.btn', 'click', (event, target) => {
                const id = target.getAttribute('data-id');
                log(`基础委托: 点击了按钮 ${id} (${target.textContent.trim()})`);
            });
            log('基础委托已设置');
        };

        // 设置高级委托
        window.setupAdvancedDelegate = function() {
            const container2 = document.getElementById('container2');
            removeAdvancedDelegateFunc = delegateAdvanced(container2, '.btn', 'click', (event, target) => {
                const id = target.getAttribute('data-id');
                log(`高级委托: 点击了按钮 ${id} (${target.textContent.trim()})`);
            });
            log('高级委托已设置');
        };

        // 移除高级委托
        window.removeAdvancedDelegate = function() {
            if (removeAdvancedDelegateFunc) {
                removeAdvancedDelegateFunc();
                removeAdvancedDelegateFunc = null;
                log('高级委托已移除');
            } else {
                log('没有可移除的高级委托');
            }
        };

        // 动态添加按钮
        let buttonCounter = 8;
        window.addNewButton = function() {
            const container1 = document.getElementById('container1');
            const container2 = document.getElementById('container2');
            
            const btn1 = document.createElement('button');
            btn1.className = 'btn';
            btn1.setAttribute('data-id', buttonCounter);
            btn1.textContent = `动态按钮 ${buttonCounter}`;
            container1.appendChild(btn1);
            
            const btn2 = document.createElement('button');
            btn2.className = 'btn';
            btn2.setAttribute('data-id', buttonCounter + 1);
            btn2.textContent = `动态按钮 ${buttonCounter + 1}`;
            container2.appendChild(btn2);
            
            log(`添加了动态按钮 ${buttonCounter} 和 ${buttonCounter + 1}`);
            buttonCounter += 2;
        };

        // 清空日志
        window.clearLog = function() {
            const logElement = document.getElementById('log');
            logElement.innerHTML = '<div>事件日志将显示在这里...</div>';
        };

        // 页面加载完成后的初始化
        document.addEventListener('DOMContentLoaded', () => {
            log('页面加载完成，可以开始测试事件委托功能');
        });
    </script>
</body>
</html>
