<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>DOM工具库测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        .test-box {
            width: 100px;
            height: 100px;
            background-color: #f0f0f0;
            border: 1px solid #ccc;
            margin: 10px;
            display: inline-block;
            text-align: center;
            line-height: 100px;
        }
        .active {
            background-color: #007bff;
            color: white;
        }
        .hidden {
            display: none;
        }
        button {
            margin: 5px;
            padding: 8px 16px;
            cursor: pointer;
        }
        .fade-box {
            width: 200px;
            height: 100px;
            background-color: #28a745;
            color: white;
            text-align: center;
            line-height: 100px;
            margin: 10px 0;
        }
        .slide-box {
            background-color: #ffc107;
            padding: 20px;
            margin: 10px 0;
            overflow: hidden;
        }
    </style>
</head>
<body>
    <h1>DOM工具库功能测试</h1>

    <div class="test-section">
        <h2>基础查询和操作</h2>
        <div id="test-container">
            <div class="test-box" data-index="0">Box 1</div>
            <div class="test-box" data-index="1">Box 2</div>
            <div class="test-box" data-index="2">Box 3</div>
        </div>
        <button onclick="testBasicOperations()">测试基础操作</button>
        <button onclick="testClassOperations()">测试类名操作</button>
        <button onclick="testActiveByIndex()">测试索引激活</button>
    </div>

    <div class="test-section">
        <h2>动画效果</h2>
        <div class="fade-box" id="fade-box">淡入淡出测试</div>
        <button onclick="testFadeIn()">淡入</button>
        <button onclick="testFadeOut()">淡出</button>
        
        <div class="slide-box" id="slide-box">
            <p>这是滑动测试内容</p>
            <p>可以测试滑动显示和隐藏效果</p>
        </div>
        <button onclick="testSlideDown()">滑动显示</button>
        <button onclick="testSlideUp()">滑动隐藏</button>
    </div>

    <div class="test-section">
        <h2>事件处理</h2>
        <div id="event-test">
            <button id="once-btn">一次性事件</button>
            <button id="debounce-btn">防抖事件</button>
            <button id="throttle-btn">节流事件</button>
            <div id="delegate-container">
                <button class="delegate-btn">委托按钮1</button>
                <button class="delegate-btn">委托按钮2</button>
                <button class="delegate-btn">委托按钮3</button>
            </div>
        </div>
        <div id="event-log"></div>
    </div>

    <div class="test-section">
        <h2>表单处理</h2>
        <form id="test-form">
            <div>
                <label>姓名: <input type="text" name="name" value=""></label>
            </div>
            <div>
                <label>邮箱: <input type="email" name="email" value=""></label>
            </div>
            <div>
                <label>性别: 
                    <input type="radio" name="gender" value="male"> 男
                    <input type="radio" name="gender" value="female"> 女
                </label>
            </div>
            <div>
                <label>爱好: 
                    <input type="checkbox" name="hobbies" value="reading"> 阅读
                    <input type="checkbox" name="hobbies" value="music"> 音乐
                    <input type="checkbox" name="hobbies" value="sports"> 运动
                </label>
            </div>
            <button type="button" onclick="testFormData()">获取表单数据</button>
            <button type="button" onclick="testSetFormData()">设置表单数据</button>
        </form>
    </div>

    <div class="test-section">
        <h2>实用工具</h2>
        <button onclick="testCopyToClipboard()">复制文本到剪贴板</button>
        <button onclick="testWaitForElement()">等待元素出现</button>
        <button onclick="testElementCreation()">创建元素</button>
        <div id="utility-result"></div>
    </div>

    <script type="module">
        // 这里将导入DOM工具库并进行测试
        // 由于是模块化的，需要构建工具支持
        console.log('DOM工具库测试页面已加载');
    </script>
</body>
</html>
