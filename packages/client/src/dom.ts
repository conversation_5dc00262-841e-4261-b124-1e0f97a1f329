import { ensureArray } from './utils'

/**
 * DOM操作工具库
 * 提供简化的DOM操作方法
 */

export function query(selector: string): HTMLElement
export function query(element: ParentNode, selector: string): HTMLElement
export function query(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement {
  if (typeof elementOrSelector === 'string') {
    return query(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return elementOrSelector.querySelector(selector) as HTMLElement
}

export function queryAll(selector: string): HTMLElement[]
export function queryAll(element: ParentNode, selector: string): HTMLElement[]
export function queryAll(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement[] {
  if (typeof elementOrSelector === 'string') {
    return queryAll(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return Array.from(elementOrSelector.querySelectorAll(selector))
}

export function addClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.add(...classNames))
}

export function removeClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.remove(...classNames))
}

export function toggleClass(
  target: HTMLElement | HTMLElement[],
  className: string,
  force?: boolean,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.toggle(className, force))
}

export function setActiveByIndex(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
  activeIndex: number,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.remove(...classNames))
  if (elements[activeIndex]) {
    elements[activeIndex].classList.add(...classNames)
  }
}

export function setMultipleActiveByIndex(
  groups: { elements: HTMLElement[]; classNames: string[] }[],
  activeIndex: number,
): void {
  groups.forEach(({ elements, classNames }) => {
    setActiveByIndex(elements, classNames, activeIndex)
  })
}

export function setStyle(
  target: HTMLElement | HTMLElement[],
  styles: Partial<CSSStyleDeclaration>,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    Object.assign(el.style, styles)
  })
}

export function setAttr(
  target: HTMLElement | HTMLElement[],
  attributes: Record<string, string>,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    Object.entries(attributes).forEach(([key, value]) => {
      el.setAttribute(key, value)
    })
  })
}

export function removeAttr(
  target: HTMLElement | HTMLElement[],
  ...attributes: string[]
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    attributes.forEach((attr) => el.removeAttribute(attr))
  })
}

const listenerMap = new WeakMap<Function, EventListenerOrEventListenerObject>()

export function on<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | AddEventListenerOptions,
): void {
  const elements = ensureArray(target)

  elements.forEach((el, index) => {
    if (listenerMap.has(listener)) {
      const wrappedListener = listenerMap.get(listener)
      if (wrappedListener) {
        el.addEventListener(type, wrappedListener, options)
        return
      }
    }

    const wrappedListener = function (event: Event) {
      listener.call(el, event as HTMLElementEventMap[K], index)
    }

    listenerMap.set(listener, wrappedListener)

    el.addEventListener(type, wrappedListener, options)
  })
}

export function off<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | EventListenerOptions,
): void {
  const elements = ensureArray(target)

  const wrappedListener = listenerMap.get(listener)

  if (!wrappedListener) {
    return
  }

  elements.forEach((el) => {
    el.removeEventListener(type, wrappedListener, options)
  })
}

export function show(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.style.display = ''
  })
}

export function hide(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.style.display = 'none'
  })
}

export function toggle(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    if (el.style.display === 'none') {
      show(el)
    } else {
      hide(el)
    }
  })
}
