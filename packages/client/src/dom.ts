import { ensureArray, debounce, throttle } from './utils'

/**
 * DOM操作工具库
 * 提供简化的DOM操作方法
 */

/**
 * 查询单个DOM元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素
 */
export function query(selector: string): HTMLElement
/**
 * 在指定父元素中查询单个DOM元素
 * @param element 父元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素
 */
export function query(element: ParentNode, selector: string): HTMLElement
export function query(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement {
  if (typeof elementOrSelector === 'string') {
    return query(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return elementOrSelector.querySelector(selector) as HTMLElement
}

/**
 * 查询所有匹配的DOM元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素数组
 */
export function queryAll(selector: string): HTMLElement[]
/**
 * 在指定父元素中查询所有匹配的DOM元素
 * @param element 父元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素数组
 */
export function queryAll(element: ParentNode, selector: string): HTMLElement[]
export function queryAll(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement[] {
  if (typeof elementOrSelector === 'string') {
    return queryAll(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return Array.from(elementOrSelector.querySelectorAll(selector))
}

/**
 * 为元素添加CSS类名
 * @param target 目标元素或元素数组
 * @param classNames 要添加的类名数组
 */
export function addClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.add(...classNames))
}

/**
 * 从元素中移除CSS类名
 * @param target 目标元素或元素数组
 * @param classNames 要移除的类名数组
 */
export function removeClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.remove(...classNames))
}

/**
 * 切换元素的CSS类名
 * @param target 目标元素或元素数组
 * @param className 要切换的类名
 * @param force 可选，强制添加(true)或移除(false)
 */
export function toggleClass(
  target: HTMLElement | HTMLElement[],
  className: string,
  force?: boolean,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.toggle(className, force))
}

/**
 * 根据索引设置激活状态的元素
 * @param target 目标元素或元素数组
 * @param classNames 激活状态的类名数组
 * @param activeIndex 要激活的元素索引
 */
export function setActiveByIndex(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
  activeIndex: number,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.remove(...classNames))
  if (elements[activeIndex]) {
    elements[activeIndex].classList.add(...classNames)
  }
}

/**
 * 为多个元素组根据索引设置激活状态
 * @param groups 元素组数组，每个组包含elements和classNames
 * @param activeIndex 要激活的元素索引
 */
export function setMultipleActiveByIndex(
  groups: { elements: HTMLElement[]; classNames: string[] }[],
  activeIndex: number,
): void {
  groups.forEach(({ elements, classNames }) => {
    setActiveByIndex(elements, classNames, activeIndex)
  })
}

/**
 * 设置元素的CSS样式
 * @param target 目标元素或元素数组
 * @param styles CSS样式对象
 */
export function setStyle(
  target: HTMLElement | HTMLElement[],
  styles: Partial<CSSStyleDeclaration>,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    Object.assign(el.style, styles)
  })
}

/**
 * 设置元素的属性
 * @param target 目标元素或元素数组
 * @param attributes 属性键值对对象
 */
export function setAttr(
  target: HTMLElement | HTMLElement[],
  attributes: Record<string, string>,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    Object.entries(attributes).forEach(([key, value]) => {
      el.setAttribute(key, value)
    })
  })
}

/**
 * 移除元素的属性
 * @param target 目标元素或元素数组
 * @param attrNames 要移除的属性名列表
 */
export function removeAttr(
  target: HTMLElement | HTMLElement[],
  ...attrNames: string[]
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    attrNames.forEach((attrName) => el.removeAttribute(attrName))
  })
}

/**
 * 切换元素的属性
 * @param target 目标元素或元素数组
 * @param name 要切换的属性名称的字符串
 * @param force 可选，强制添加(true)或移除(false)
 */
export function toggleAttr(
  target: HTMLElement | HTMLElement[],
  name: string,
  force?: boolean,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.toggleAttribute(name, force)
  })
}

const listenerMap = new WeakMap<Function, EventListenerOrEventListenerObject>()

/**
 * 为元素添加事件监听器
 * @param target 目标元素或元素数组
 * @param type 事件类型
 * @param listener 事件处理函数
 * @param options 事件监听选项
 */
export function on<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | AddEventListenerOptions,
): void {
  const elements = ensureArray(target)

  elements.forEach((el, index) => {
    if (listenerMap.has(listener)) {
      const wrappedListener = listenerMap.get(listener)
      if (wrappedListener) {
        el.addEventListener(type, wrappedListener, options)
        return
      }
    }

    const wrappedListener = function (event: Event) {
      listener.call(el, event as HTMLElementEventMap[K], index)
    }

    listenerMap.set(listener, wrappedListener)

    el.addEventListener(type, wrappedListener, options)
  })
}

/**
 * 移除元素的事件监听器
 * @param target 目标元素或元素数组
 * @param type 事件类型
 * @param listener 要移除的事件处理函数
 * @param options 事件监听选项
 */
export function off<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | EventListenerOptions,
): void {
  const elements = ensureArray(target)

  const wrappedListener = listenerMap.get(listener)

  if (!wrappedListener) {
    return
  }

  elements.forEach((el) => {
    el.removeEventListener(type, wrappedListener, options)
  })
}

/**
 * 一次性事件监听器
 * @param target 目标元素
 * @param type 事件类型
 * @param listener 事件处理函数
 */
export function once<
  T extends EventTarget,
  K extends keyof HTMLElementEventMap,
>(target: T, type: K, listener: (event: HTMLElementEventMap[K]) => void): void {
  const onceListener = (event: Event) => {
    listener(event as HTMLElementEventMap[K])
    target.removeEventListener(type, onceListener as EventListener)
  }
  target.addEventListener(type, onceListener as EventListener)
}

/**
 * 事件委托
 * @param container 容器元素
 * @param selector 目标选择器
 * @param type 事件类型
 * @param listener 事件处理函数
 */
export function delegate<K extends keyof HTMLElementEventMap>(
  container: HTMLElement,
  selector: string,
  type: K,
  listener: (event: HTMLElementEventMap[K], target: HTMLElement) => void,
): void {
  container.addEventListener(type, (event) => {
    const target = event.target as HTMLElement
    if (target.closest(selector)) {
      listener(event as HTMLElementEventMap[K], target)
    }
  })
}

/**
 * 显示元素
 * @param target 目标元素或元素数组
 */
export function show(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.style.display = ''
  })
}

/**
 * 隐藏元素，通过 display = 'none'
 * @param target 目标元素或元素数组
 */
export function hide(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.style.display = 'none'
  })
}

/**
 * 切换元素的显示/隐藏状态
 * @param target 目标元素或元素数组
 */
export function toggle(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    if (el.style.display === 'none') {
      show(el)
    } else {
      hide(el)
    }
  })
}

/**
 * 获取或设置元素的文本内容
 * @param target 目标元素
 * @param text 要设置的文本（可选）
 * @returns 如果是获取模式，返回文本内容
 */
export function text(target: HTMLElement, text?: string): string | void {
  if (text === undefined) {
    return target.textContent || ''
  }
  target.textContent = text
}

/**
 * 获取或设置元素的HTML内容
 * @param target 目标元素
 * @param html 要设置的HTML（可选）
 * @returns 如果是获取模式，返回HTML内容
 */
export function html(target: HTMLElement, html?: string): string | void {
  if (html === undefined) {
    return target.innerHTML
  }
  target.innerHTML = html
}

/**
 * 在指定元素前插入新元素
 * @param target 目标元素
 * @param newElement 要插入的新元素
 */
export function insertBefore<T extends Node>(target: T, newElement: T): void {
  target.parentNode?.insertBefore(newElement, target)
}

/**
 * 在指定元素后插入新元素
 * @param target 目标元素
 * @param newElement 要插入的新元素
 */
export function insertAfter<T extends Node>(target: T, newElement: T): void {
  target.parentNode?.insertBefore(newElement, target.nextSibling)
}

/**
 * 移除元素
 * @param target 要移除的元素或元素数组
 */
export function remove(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.remove())
}

/**
 * 检查元素是否包含指定类名
 * @param target 目标元素
 * @param className 类名
 * @returns 是否包含指定类名
 */
export function hasClass(target: HTMLElement, className: string): boolean {
  return target.classList.contains(className)
}

/**
 * 检查元素是否可见
 * @param target 目标元素
 * @returns 是否可见
 */
export function isVisible(target: HTMLElement): boolean {
  const style = window.getComputedStyle(target)
  return (
    style.display !== 'none' &&
    style.visibility !== 'hidden' &&
    style.opacity !== '0' &&
    target.offsetWidth > 0 &&
    target.offsetHeight > 0
  )
}

/**
 * 滚动到指定元素
 * @param target 目标元素
 * @param options 滚动选项
 */
export function scrollTo(
  target: HTMLElement,
  options?: ScrollIntoViewOptions,
): void {
  target.scrollIntoView({
    behavior: 'smooth',
    block: 'start',
    inline: 'nearest',
    ...options,
  })
}

/**
 * 淡入动画
 * @param target 目标元素
 * @param duration 动画持续时间（毫秒）
 * @returns Promise，动画完成时resolve
 */
export function fadeIn(
  target: HTMLElement,
  duration: number = 300,
): Promise<void> {
  return new Promise((resolve) => {
    target.style.opacity = '0'
    target.style.display = ''
    target.style.transition = `opacity ${duration}ms ease-in-out`

    requestAnimationFrame(() => {
      target.style.opacity = '1'
    })

    setTimeout(() => {
      target.style.transition = ''
      resolve()
    }, duration)
  })
}

/**
 * 淡出动画
 * @param target 目标元素
 * @param duration 动画持续时间（毫秒）
 * @returns Promise，动画完成时resolve
 */
export function fadeOut(
  target: HTMLElement,
  duration: number = 300,
): Promise<void> {
  return new Promise((resolve) => {
    target.style.opacity = '1'
    target.style.transition = `opacity ${duration}ms ease-in-out`

    requestAnimationFrame(() => {
      target.style.opacity = '0'
    })

    setTimeout(() => {
      target.style.display = 'none'
      target.style.transition = ''
      resolve()
    }, duration)
  })
}

/**
 * 滑动显示
 * @param target 目标元素
 * @param duration 动画持续时间（毫秒）
 * @returns Promise，动画完成时resolve
 */
export function slideDown(
  target: HTMLElement,
  duration: number = 300,
): Promise<void> {
  return new Promise((resolve) => {
    const originalHeight = target.scrollHeight
    target.style.height = '0'
    target.style.overflow = 'hidden'
    target.style.display = ''
    target.style.transition = `height ${duration}ms ease-in-out`

    requestAnimationFrame(() => {
      target.style.height = `${originalHeight}px`
    })

    setTimeout(() => {
      target.style.height = ''
      target.style.overflow = ''
      target.style.transition = ''
      resolve()
    }, duration)
  })
}

/**
 * 滑动隐藏
 * @param target 目标元素
 * @param duration 动画持续时间（毫秒）
 * @returns Promise，动画完成时resolve
 */
export function slideUp(
  target: HTMLElement,
  duration: number = 300,
): Promise<void> {
  return new Promise((resolve) => {
    const originalHeight = target.scrollHeight
    target.style.height = `${originalHeight}px`
    target.style.overflow = 'hidden'
    target.style.transition = `height ${duration}ms ease-in-out`

    requestAnimationFrame(() => {
      target.style.height = '0'
    })

    setTimeout(() => {
      target.style.display = 'none'
      target.style.height = ''
      target.style.overflow = ''
      target.style.transition = ''
      resolve()
    }, duration)
  })
}

/**
 * 等待元素出现在DOM中
 * @param selector 选择器
 * @param timeout 超时时间（毫秒）
 * @returns Promise，元素出现时resolve
 */
export function waitForElement(
  selector: string,
  timeout: number = 5000,
): Promise<HTMLElement> {
  return new Promise((resolve, reject) => {
    const element = document.querySelector(selector) as HTMLElement
    if (element) {
      resolve(element)
      return
    }

    const observer = new MutationObserver(() => {
      const element = document.querySelector(selector) as HTMLElement
      if (element) {
        observer.disconnect()
        clearTimeout(timeoutId)
        resolve(element)
      }
    })

    const timeoutId = setTimeout(() => {
      observer.disconnect()
      reject(
        new Error(
          `Element with selector "${selector}" not found within ${timeout}ms`,
        ),
      )
    }, timeout)

    observer.observe(document.body, {
      childList: true,
      subtree: true,
    })
  })
}

/**
 * 复制文本到剪贴板
 * @param text 要复制的文本
 * @returns Promise，复制完成时resolve
 */
export function copyToClipboard(text: string): Promise<void> {
  if (navigator.clipboard && window.isSecureContext) {
    return navigator.clipboard.writeText(text)
  } else {
    // 降级方案
    return new Promise((resolve, reject) => {
      const textArea = document.createElement('textarea')
      textArea.value = text
      textArea.style.position = 'fixed'
      textArea.style.left = '-999999px'
      textArea.style.top = '-999999px'
      document.body.appendChild(textArea)
      textArea.focus()
      textArea.select()

      try {
        document.execCommand('copy')
        textArea.remove()
        resolve()
      } catch (error) {
        textArea.remove()
        reject(error)
      }
    })
  }
}
