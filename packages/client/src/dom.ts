import { ensureArray } from './utils'

/**
 * DOM操作工具库
 * 提供简化的DOM操作方法
 */

/**
 * 查询单个DOM元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素
 */
export function query(selector: string): HTMLElement
/**
 * 在指定父元素中查询单个DOM元素
 * @param element 父元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素
 */
export function query(element: ParentNode, selector: string): HTMLElement
export function query(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement {
  if (typeof elementOrSelector === 'string') {
    return query(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return elementOrSelector.querySelector(selector) as HTMLElement
}

/**
 * 查询所有匹配的DOM元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素数组
 */
export function queryAll(selector: string): HTMLElement[]
/**
 * 在指定父元素中查询所有匹配的DOM元素
 * @param element 父元素
 * @param selector CSS选择器字符串
 * @returns 匹配的HTML元素数组
 */
export function queryAll(element: ParentNode, selector: string): HTMLElement[]
export function queryAll(
  elementOrSelector: ParentNode | string,
  selector?: string,
): HTMLElement[] {
  if (typeof elementOrSelector === 'string') {
    return queryAll(document, elementOrSelector)
  }
  if (!selector) {
    throw new Error('selector is required')
  }
  return Array.from(elementOrSelector.querySelectorAll(selector))
}

/**
 * 为元素添加CSS类名
 * @param target 目标元素或元素数组
 * @param classNames 要添加的类名数组
 */
export function addClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.add(...classNames))
}

/**
 * 从元素中移除CSS类名
 * @param target 目标元素或元素数组
 * @param classNames 要移除的类名数组
 */
export function removeClass(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.remove(...classNames))
}

/**
 * 切换元素的CSS类名
 * @param target 目标元素或元素数组
 * @param className 要切换的类名
 * @param force 可选，强制添加(true)或移除(false)
 */
export function toggleClass(
  target: HTMLElement | HTMLElement[],
  className: string,
  force?: boolean,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.toggle(className, force))
}

/**
 * 根据索引设置激活状态的元素
 * @param target 目标元素或元素数组
 * @param classNames 激活状态的类名数组
 * @param activeIndex 要激活的元素索引
 */
export function setActiveByIndex(
  target: HTMLElement | HTMLElement[],
  classNames: string[],
  activeIndex: number,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => el.classList.remove(...classNames))
  if (elements[activeIndex]) {
    elements[activeIndex].classList.add(...classNames)
  }
}

/**
 * 为多个元素组根据索引设置激活状态
 * @param groups 元素组数组，每个组包含elements和classNames
 * @param activeIndex 要激活的元素索引
 */
export function setMultipleActiveByIndex(
  groups: { elements: HTMLElement[]; classNames: string[] }[],
  activeIndex: number,
): void {
  groups.forEach(({ elements, classNames }) => {
    setActiveByIndex(elements, classNames, activeIndex)
  })
}

/**
 * 设置元素的CSS样式
 * @param target 目标元素或元素数组
 * @param styles CSS样式对象
 */
export function setStyle(
  target: HTMLElement | HTMLElement[],
  styles: Partial<CSSStyleDeclaration>,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    Object.assign(el.style, styles)
  })
}

/**
 * 设置元素的属性
 * @param target 目标元素或元素数组
 * @param attributes 属性键值对对象
 */
export function setAttr(
  target: HTMLElement | HTMLElement[],
  attributes: Record<string, string>,
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    Object.entries(attributes).forEach(([key, value]) => {
      el.setAttribute(key, value)
    })
  })
}

/**
 * 移除元素的属性
 * @param target 目标元素或元素数组
 * @param attributes 要移除的属性名列表
 */
export function removeAttr(
  target: HTMLElement | HTMLElement[],
  ...attributes: string[]
): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    attributes.forEach((attr) => el.removeAttribute(attr))
  })
}

const listenerMap = new WeakMap<Function, EventListenerOrEventListenerObject>()

/**
 * 为元素添加事件监听器
 * @param target 目标元素或元素数组
 * @param type 事件类型
 * @param listener 事件处理函数
 * @param options 事件监听选项
 */
export function on<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | AddEventListenerOptions,
): void {
  const elements = ensureArray(target)

  elements.forEach((el, index) => {
    if (listenerMap.has(listener)) {
      const wrappedListener = listenerMap.get(listener)
      if (wrappedListener) {
        el.addEventListener(type, wrappedListener, options)
        return
      }
    }

    const wrappedListener = function (event: Event) {
      listener.call(el, event as HTMLElementEventMap[K], index)
    }

    listenerMap.set(listener, wrappedListener)

    el.addEventListener(type, wrappedListener, options)
  })
}

/**
 * 移除元素的事件监听器
 * @param target 目标元素或元素数组
 * @param type 事件类型
 * @param listener 要移除的事件处理函数
 * @param options 事件监听选项
 */
export function off<T extends EventTarget, K extends keyof HTMLElementEventMap>(
  target: T | T[],
  type: K,
  listener: (this: T, event: HTMLElementEventMap[K], index: number) => void,
  options?: boolean | EventListenerOptions,
): void {
  const elements = ensureArray(target)

  const wrappedListener = listenerMap.get(listener)

  if (!wrappedListener) {
    return
  }

  elements.forEach((el) => {
    el.removeEventListener(type, wrappedListener, options)
  })
}

/**
 * 显示元素
 * @param target 目标元素或元素数组
 */
export function show(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.style.display = ''
  })
}

/**
 * 隐藏元素，通过 display = 'none'
 * @param target 目标元素或元素数组
 */
export function hide(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    el.style.display = 'none'
  })
}

/**
 * 切换元素的显示/隐藏状态
 * @param target 目标元素或元素数组
 */
export function toggle(target: HTMLElement | HTMLElement[]): void {
  const elements = ensureArray(target)
  elements.forEach((el) => {
    if (el.style.display === 'none') {
      show(el)
    } else {
      hide(el)
    }
  })
}
