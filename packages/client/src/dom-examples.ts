import * as DOM from './dom'

/**
 * DOM工具库使用示例
 * 展示所有新增功能的用法
 */

// ==================== 基础查询和操作示例 ====================

// 查询元素
const container = DOM.query('#test-container')
const boxes = DOM.queryAll('.test-box')

// 创建新元素
const newBox = DOM.createElement('div', 
  { class: 'test-box', 'data-index': '3' }, 
  ['New Box']
)

// 插入元素
DOM.insertAfter(boxes[boxes.length - 1], newBox)

// 设置文本和HTML
DOM.text(newBox, '新建的盒子')
DOM.html(container, DOM.html(container) + '<p>添加了新内容</p>')

// ==================== 样式和类名操作示例 ====================

// 检查类名
if (DOM.hasClass(boxes[0], 'active')) {
  console.log('第一个盒子是激活状态')
}

// 切换类名
DOM.toggleClass(boxes, 'highlight')

// 设置样式
DOM.setStyle(boxes, {
  transition: 'all 0.3s ease',
  cursor: 'pointer'
})

// 设置属性
DOM.setAttr(boxes, { 'data-clickable': 'true' })

// ==================== 状态检查示例 ====================

// 检查可见性
boxes.forEach((box, index) => {
  if (DOM.isVisible(box)) {
    console.log(`盒子 ${index + 1} 是可见的`)
  }
})

// 检查是否在视口中
const checkViewport = () => {
  boxes.forEach((box, index) => {
    if (DOM.isInViewport(box, 0.5)) {
      console.log(`盒子 ${index + 1} 在视口中（50%可见）`)
    }
  })
}

// 获取位置信息
const position = DOM.getPosition(container)
console.log('容器位置:', position)

const offset = DOM.getOffset(container)
console.log('容器偏移:', offset)

// ==================== 事件处理示例 ====================

// 基础事件监听
DOM.on(boxes, 'click', (event, index) => {
  console.log(`点击了第 ${index + 1} 个盒子`)
  DOM.setActiveByIndex(boxes, ['active'], index)
})

// 一次性事件
DOM.once(document.querySelector('#once-btn')!, 'click', () => {
  alert('这个事件只会触发一次！')
})

// 防抖事件
DOM.onDebounced(document.querySelector('#search-input')!, 'input', (event) => {
  const target = event.target as HTMLInputElement
  console.log('搜索:', target.value)
}, 300)

// 节流事件
DOM.onThrottled(window as any, 'scroll', () => {
  checkViewport()
}, 100)

// 事件委托
DOM.delegate(document.querySelector('#delegate-container')!, '.delegate-btn', 'click', (event, target) => {
  console.log('委托事件触发:', DOM.text(target))
})

// 触发自定义事件
DOM.trigger(container, 'customEvent', { message: '自定义事件数据' })

// ==================== 动画效果示例 ====================

// 淡入淡出
async function fadeExample() {
  const fadeBox = DOM.query('#fade-box')
  
  await DOM.fadeOut(fadeBox, 500)
  console.log('淡出完成')
  
  await DOM.fadeIn(fadeBox, 500)
  console.log('淡入完成')
}

// 滑动效果
async function slideExample() {
  const slideBox = DOM.query('#slide-box')
  
  await DOM.slideUp(slideBox, 400)
  console.log('滑动隐藏完成')
  
  await DOM.slideDown(slideBox, 400)
  console.log('滑动显示完成')
}

// ==================== 表单处理示例 ====================

// 获取表单数据
function getFormExample() {
  const form = DOM.query('#test-form') as HTMLFormElement
  const data = DOM.getFormData(form)
  console.log('表单数据:', data)
  return data
}

// 设置表单数据
function setFormExample() {
  const form = DOM.query('#test-form') as HTMLFormElement
  const sampleData = {
    name: '张三',
    email: '<EMAIL>',
    gender: 'male',
    hobbies: ['reading', 'music']
  }
  
  DOM.setFormData(form, sampleData)
  console.log('表单数据已设置')
}

// 验证表单
function validateFormExample() {
  const form = DOM.query('#test-form') as HTMLFormElement
  const isValid = DOM.validateForm(form)
  console.log('表单是否有效:', isValid)
  return isValid
}

// ==================== 实用工具示例 ====================

// 复制到剪贴板
async function copyExample() {
  try {
    await DOM.copyToClipboard('这是要复制的文本内容')
    console.log('文本已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
  }
}

// 等待元素出现
async function waitForElementExample() {
  try {
    // 模拟动态添加元素
    setTimeout(() => {
      const dynamicElement = DOM.createElement('div', { id: 'dynamic-element' }, ['动态元素'])
      document.body.appendChild(dynamicElement)
    }, 2000)
    
    const element = await DOM.waitForElement('#dynamic-element', 5000)
    console.log('动态元素已出现:', element)
  } catch (error) {
    console.error('等待元素超时:', error)
  }
}

// ==================== 综合示例 ====================

// 创建一个交互式的图片画廊
function createImageGallery() {
  const gallery = DOM.createElement('div', { class: 'image-gallery' })
  const images = ['image1.jpg', 'image2.jpg', 'image3.jpg']
  
  images.forEach((src, index) => {
    const img = DOM.createElement('img', {
      src,
      class: 'gallery-image',
      'data-index': index.toString()
    })
    
    // 添加点击事件
    DOM.on(img, 'click', async () => {
      // 高亮当前图片
      DOM.setActiveByIndex(DOM.queryAll('.gallery-image'), ['active'], index)
      
      // 淡入效果
      await DOM.fadeOut(img, 200)
      await DOM.fadeIn(img, 200)
    })
    
    gallery.appendChild(img)
  })
  
  return gallery
}

// 导出示例函数供测试使用
export {
  fadeExample,
  slideExample,
  getFormExample,
  setFormExample,
  validateFormExample,
  copyExample,
  waitForElementExample,
  createImageGallery
}
