# DOM工具库 API 文档

这是一个功能丰富的DOM操作工具库，提供了简化的DOM操作方法，包含了从基础查询到高级动画的完整功能集。

## 目录

- [基础查询](#基础查询)
- [元素创建和操作](#元素创建和操作)
- [样式和类名操作](#样式和类名操作)
- [元素状态检查](#元素状态检查)
- [尺寸和位置](#尺寸和位置)
- [事件处理](#事件处理)
- [动画效果](#动画效果)
- [表单处理](#表单处理)
- [实用工具](#实用工具)

## 基础查询

### `query(selector: string): HTMLElement`
查询单个DOM元素
```typescript
const element = query('#my-id')
const button = query('.btn-primary')
```

### `query(element: ParentNode, selector: string): HTMLElement`
在指定父元素中查询单个DOM元素
```typescript
const container = query('#container')
const child = query(container, '.child')
```

### `queryAll(selector: string): HTMLElement[]`
查询所有匹配的DOM元素
```typescript
const buttons = queryAll('.btn')
const items = queryAll('li')
```

## 元素创建和操作

### `createElement(tagName: string, attributes?: Record<string, string>, children?: (HTMLElement | string)[]): HTMLElement`
创建DOM元素
```typescript
const div = createElement('div', { class: 'container', id: 'main' }, ['Hello World'])
const button = createElement('button', { type: 'button' }, ['Click me'])
```

### `text(target: HTMLElement, text?: string): string | void`
获取或设置元素的文本内容
```typescript
const content = text(element) // 获取文本
text(element, 'New text') // 设置文本
```

### `html(target: HTMLElement, html?: string): string | void`
获取或设置元素的HTML内容
```typescript
const content = html(element) // 获取HTML
html(element, '<span>New HTML</span>') // 设置HTML
```

### `insertBefore(target: HTMLElement, newElement: HTMLElement): void`
在指定元素前插入新元素
```typescript
insertBefore(targetElement, newElement)
```

### `insertAfter(target: HTMLElement, newElement: HTMLElement): void`
在指定元素后插入新元素
```typescript
insertAfter(targetElement, newElement)
```

### `remove(target: HTMLElement | HTMLElement[]): void`
移除元素
```typescript
remove(element)
remove([element1, element2])
```

## 样式和类名操作

### `addClass(target: HTMLElement | HTMLElement[], classNames: string[]): void`
为元素添加CSS类名
```typescript
addClass(element, ['active', 'highlight'])
addClass([el1, el2], ['visible'])
```

### `removeClass(target: HTMLElement | HTMLElement[], classNames: string[]): void`
从元素中移除CSS类名
```typescript
removeClass(element, ['active', 'highlight'])
```

### `toggleClass(target: HTMLElement | HTMLElement[], className: string, force?: boolean): void`
切换元素的CSS类名
```typescript
toggleClass(element, 'active')
toggleClass(element, 'visible', true) // 强制添加
```

### `hasClass(target: HTMLElement, className: string): boolean`
检查元素是否包含指定类名
```typescript
if (hasClass(element, 'active')) {
  console.log('元素是激活状态')
}
```

### `setStyle(target: HTMLElement | HTMLElement[], styles: Partial<CSSStyleDeclaration>): void`
设置元素的CSS样式
```typescript
setStyle(element, { color: 'red', fontSize: '16px' })
```

### `setAttr(target: HTMLElement | HTMLElement[], attributes: Record<string, string>): void`
设置元素的属性
```typescript
setAttr(element, { 'data-id': '123', 'aria-label': 'Button' })
```

### `removeAttr(target: HTMLElement | HTMLElement[], ...attributes: string[]): void`
移除元素的属性
```typescript
removeAttr(element, 'data-id', 'aria-label')
```

## 元素状态检查

### `isVisible(target: HTMLElement): boolean`
检查元素是否可见
```typescript
if (isVisible(element)) {
  console.log('元素可见')
}
```

### `isInViewport(target: HTMLElement, threshold?: number): boolean`
检查元素是否在视口中
```typescript
if (isInViewport(element, 0.5)) {
  console.log('元素50%在视口中')
}
```

### `getComputedStyle(target: HTMLElement, property?: string): CSSStyleDeclaration | string`
获取元素的计算样式
```typescript
const styles = getComputedStyle(element)
const color = getComputedStyle(element, 'color')
```

## 尺寸和位置

### `getPosition(target: HTMLElement): { top: number; left: number; width: number; height: number }`
获取元素的位置信息
```typescript
const { top, left, width, height } = getPosition(element)
```

### `getOffset(target: HTMLElement): { top: number; left: number }`
获取元素相对于文档的偏移量
```typescript
const { top, left } = getOffset(element)
```

### `scrollTo(target: HTMLElement, options?: ScrollIntoViewOptions): void`
滚动到指定元素
```typescript
scrollTo(element, { behavior: 'smooth', block: 'center' })
```

## 事件处理

### `on<T, K>(target: T | T[], type: K, listener: Function, options?: boolean | AddEventListenerOptions): void`
为元素添加事件监听器
```typescript
on(button, 'click', (event, index) => {
  console.log('按钮被点击', index)
})
```

### `off<T, K>(target: T | T[], type: K, listener: Function, options?: boolean | EventListenerOptions): void`
移除元素的事件监听器
```typescript
off(button, 'click', clickHandler)
```

### `once<T, K>(target: T, type: K, listener: Function): void`
一次性事件监听器
```typescript
once(button, 'click', () => {
  console.log('只会执行一次')
})
```

### `delegate<K>(container: HTMLElement, selector: string, type: K, listener: Function): void`
事件委托
```typescript
delegate(container, '.btn', 'click', (event, target) => {
  console.log('委托事件触发')
})
```

### `trigger(target: HTMLElement, eventName: string, detail?: any): void`
触发自定义事件
```typescript
trigger(element, 'customEvent', { data: 'custom data' })
```

### `onDebounced<K>(target: HTMLElement, type: K, listener: Function, delay: number): void`
防抖函数包装的事件监听
```typescript
onDebounced(input, 'input', (event) => {
  console.log('防抖搜索')
}, 300)
```

### `onThrottled<K>(target: HTMLElement, type: K, listener: Function, interval: number): void`
节流函数包装的事件监听
```typescript
onThrottled(window, 'scroll', () => {
  console.log('节流滚动')
}, 100)
```

## 动画效果

### `fadeIn(target: HTMLElement, duration?: number): Promise<void>`
淡入动画
```typescript
await fadeIn(element, 500)
console.log('淡入完成')
```

### `fadeOut(target: HTMLElement, duration?: number): Promise<void>`
淡出动画
```typescript
await fadeOut(element, 500)
console.log('淡出完成')
```

### `slideDown(target: HTMLElement, duration?: number): Promise<void>`
滑动显示
```typescript
await slideDown(element, 400)
```

### `slideUp(target: HTMLElement, duration?: number): Promise<void>`
滑动隐藏
```typescript
await slideUp(element, 400)
```

## 表单处理

### `getFormData(form: HTMLFormElement): Record<string, any>`
获取表单数据
```typescript
const data = getFormData(form)
console.log(data) // { name: 'John', email: '<EMAIL>' }
```

### `setFormData(form: HTMLFormElement, data: Record<string, any>): void`
设置表单数据
```typescript
setFormData(form, { name: 'John', email: '<EMAIL>' })
```

### `validateForm(form: HTMLFormElement): boolean`
验证表单
```typescript
if (validateForm(form)) {
  console.log('表单有效')
}
```

## 实用工具

### `show(target: HTMLElement | HTMLElement[]): void`
显示元素
```typescript
show(element)
```

### `hide(target: HTMLElement | HTMLElement[]): void`
隐藏元素
```typescript
hide(element)
```

### `toggle(target: HTMLElement | HTMLElement[]): void`
切换元素的显示/隐藏状态
```typescript
toggle(element)
```

### `setActiveByIndex(target: HTMLElement | HTMLElement[], classNames: string[], activeIndex: number): void`
根据索引设置激活状态的元素
```typescript
setActiveByIndex(tabs, ['active'], 2)
```

### `waitForElement(selector: string, timeout?: number): Promise<HTMLElement>`
等待元素出现在DOM中
```typescript
try {
  const element = await waitForElement('#dynamic-element', 5000)
  console.log('元素已出现')
} catch (error) {
  console.log('等待超时')
}
```

### `copyToClipboard(text: string): Promise<void>`
复制文本到剪贴板
```typescript
try {
  await copyToClipboard('要复制的文本')
  console.log('复制成功')
} catch (error) {
  console.log('复制失败')
}
```

## 使用示例

```typescript
import * as DOM from './dom'

// 创建一个交互式组件
const container = DOM.createElement('div', { class: 'tab-container' })

// 创建标签页
const tabs = ['首页', '关于', '联系'].map((title, index) => {
  const tab = DOM.createElement('button', 
    { class: 'tab', 'data-index': index.toString() }, 
    [title]
  )
  
  // 添加点击事件
  DOM.on(tab, 'click', () => {
    DOM.setActiveByIndex(DOM.queryAll('.tab'), ['active'], index)
  })
  
  return tab
})

// 添加到容器
tabs.forEach(tab => container.appendChild(tab))

// 添加动画效果
DOM.on(container, 'mouseenter', async () => {
  await DOM.fadeIn(container, 200)
})
```

这个工具库提供了完整的DOM操作解决方案，从基础的元素查询到复杂的动画效果，都有相应的API支持。所有方法都有完整的TypeScript类型支持和中文文档注释。
