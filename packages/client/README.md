# @tiga/client

A lightweight component system for building reactive web applications.

## Features

- 🎯 **Reactive State Management** - Built-in reactivity with automatic updates
- 🧩 **Simple Component System** - Easy component creation and registration
- 🔄 **Automatic Initialization** - Components auto-initialize when DOM elements are found
- 📦 **TypeScript Support** - Full type safety and IntelliSense
- 🚀 **Lightweight** - No heavy dependencies, pure TypeScript/JavaScript

## Installation

```bash
npm install @tiga/client
```

## Quick Start

### Basic Usage

```typescript
import { createComponent, reactive, createApp } from '@tiga/client'

// Create a component
const counter = createComponent({
  selector: '.counter',
  setup: (app, root) => {
    const count = reactive(0)
    
    const increment = () => {
      count.value++
    }
    
    // Setup DOM interactions
    const btn = root.querySelector('.increment-btn')
    const display = root.querySelector('.count-display')
    
    if (btn) {
      btn.addEventListener('click', increment)
    }
    
    if (display) {
      display.textContent = `Count: ${count.value}`
    }
    
    console.log('Counter component initialized on:', root)
  }
})

// Create and start the app
const app = createApp({ el: '#app' })
app.register(counter)
app.start()
```

### HTML Structure

```html
<div id="app">
  <div class="counter">
    <div class="count-display">Count: 0</div>
    <button class="increment-btn">Increment</button>
  </div>
</div>
```

## API Reference

### Core Functions

#### `createComponent(options)`

Creates a component that will be initialized when its DOM element is found.

**Parameters:**
- `options` (ComponentOptions): Component configuration

**Returns:** Component instance

#### `createApp(options)`

Creates an application instance.

**Parameters:**
- `options` (AppOptions): Application configuration

**Returns:** App instance

### Reactive System

#### `reactive<T>(initialValue)`

Creates a reactive state that automatically tracks changes.

```typescript
const count = reactive(0)
count.value = 1 // Triggers reactivity
```

#### `computed<T>(getter)`

Creates a computed property that automatically updates when dependencies change.

```typescript
const count = reactive(0)
const doubleCount = computed(() => count.value * 2)
```

#### `watch(source, callback)`

Watches for changes in reactive state.

```typescript
const count = reactive(0)
watch(count, (newVal, oldVal) => {
  console.log(`Count changed from ${oldVal} to ${newVal}`)
})
```

### App Methods

#### `app.register(component)`

Registers a component with the app.

#### `app.start()`

Starts the app and initializes all registered components.

### Utility Functions

#### `nextTick(callback)`

Execute callback after the next DOM update cycle.

## Component Options

```typescript
interface ComponentOptions {
  selector: string // CSS selector to find the component's DOM element
  setup: (app: App, root: HTMLElement) => void // Setup function called when element is found
}
```

## App Options

```typescript
interface AppOptions {
  el?: string | HTMLElement // Root element selector or element
}
```

## Examples

### Todo List Component

```typescript
const todoList = createComponent({
  selector: '.todo-list',
  setup: (app, root) => {
    const todos = reactive<string[]>([])
    const newTodo = reactive('')
    
    const addTodo = () => {
      if (newTodo.value.trim()) {
        todos.value.push(newTodo.value.trim())
        newTodo.value = ''
      }
    }
    
    const removeTodo = (index: number) => {
      todos.value.splice(index, 1)
    }
    
    // Setup DOM interactions
    const input = root.querySelector('input') as HTMLInputElement
    const addBtn = root.querySelector('.add-btn')
    const todoList = root.querySelector('.todo-items')
    
    if (input) {
      input.addEventListener('input', (e) => {
        newTodo.value = (e.target as HTMLInputElement).value
      })
    }
    
    if (addBtn) {
      addBtn.addEventListener('click', addTodo)
    }
    
    if (todoList) {
      watch(todos, (newTodos) => {
        todoList.innerHTML = newTodos.map((todo, index) => 
          `<div class="todo-item">
            <span>${todo}</span>
            <button onclick="this.parentElement.remove()">Remove</button>
           </div>`
        ).join('')
      })
    }
  }
})
```

### Form Component with Validation

```typescript
const formComponent = createComponent({
  selector: '.form-component',
  setup: (app, root) => {
    const form = reactive({
      name: '',
      email: '',
      password: ''
    })
    
    const errors = reactive<Record<string, string>>({})
    
    const validate = () => {
      errors.value = {}
      
      if (!form.value.name) {
        errors.value.name = 'Name is required'
      }
      
      if (!form.value.email) {
        errors.value.email = 'Email is required'
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(form.value.email)) {
        errors.value.email = 'Invalid email format'
      }
      
      if (!form.value.password) {
        errors.value.password = 'Password is required'
      } else if (form.value.password.length < 6) {
        errors.value.password = 'Password must be at least 6 characters'
      }
    }
    
    const submit = () => {
      validate()
      if (Object.keys(errors.value).length === 0) {
        console.log('Form submitted:', form.value)
        alert('Form submitted successfully!')
      } else {
        console.log('Form has errors:', errors.value)
      }
    }
    
    // Setup form interactions
    const nameInput = root.querySelector('[name="name"]') as HTMLInputElement
    const emailInput = root.querySelector('[name="email"]') as HTMLInputElement
    const passwordInput = root.querySelector('[name="password"]') as HTMLInputElement
    const submitBtn = root.querySelector('.submit-btn')
    const errorDisplay = root.querySelector('.errors')
    
    if (nameInput) {
      nameInput.addEventListener('input', (e) => {
        form.value.name = (e.target as HTMLInputElement).value
      })
    }
    
    if (emailInput) {
      emailInput.addEventListener('input', (e) => {
        form.value.email = (e.target as HTMLInputElement).value
      })
    }
    
    if (passwordInput) {
      passwordInput.addEventListener('input', (e) => {
        form.value.password = (e.target as HTMLInputElement).value
      })
    }
    
    if (submitBtn) {
      submitBtn.addEventListener('click', submit)
    }
    
    if (errorDisplay) {
      watch(errors, (newErrors) => {
        errorDisplay.innerHTML = Object.values(newErrors)
          .map(error => `<div class="error">${error}</div>`)
          .join('')
      })
    }
  }
})
```

### Multiple Components

```typescript
// Create components
const counter = createComponent({
  selector: '.counter',
  setup: (app, root) => {
    const count = reactive(0)
    const btn = root.querySelector('button')
    const display = root.querySelector('.display')
    
    if (btn) {
      btn.addEventListener('click', () => count.value++)
    }
    
    if (display) {
      watch(count, (newVal) => {
        display.textContent = `Count: ${newVal}`
      })
    }
  }
})

const todo = createComponent({
  selector: '.todo',
  setup: (app, root) => {
    // Todo implementation
  }
})

const form = createComponent({
  selector: '.form',
  setup: (app, root) => {
    // Form implementation
  }
})

// Register and start
const app = createApp({ el: '#app' })
app.register(counter)
app.register(todo)
app.register(form)
app.start()
```

## How It Works

1. **Component Creation**: Use `createComponent()` to define components with a selector and setup function
2. **Registration**: Register components with the app using `app.register()`
3. **Initialization**: Call `app.start()` to find DOM elements and initialize components
4. **Auto-Detection**: If a component's selector is found in the DOM, its setup function is called with the app instance and root element
5. **Warning**: If a selector is not found, a warning is logged to the console

## Browser Support

- Modern browsers with ES2015+ support
- No polyfills required for basic functionality

## TypeScript

The library is written in TypeScript and provides full type definitions. All APIs are fully typed for better development experience.

## License

MIT
