# DOM 工具库

这个DOM工具库提供了一系列简化DOM操作的函数，让你能够更简洁地编写DOM操作代码。

## 安装和导入

```typescript
import {
  query, queryAll,
  addClass, removeClass, toggleClass,
  setActiveByIndex, setMultipleActiveByIndex,
  setStyle, setAttr, removeAttr,
  setText, setHtml,
  on, off,
  show, hide
} from '@tiga/client'
```

## 核心功能

### 元素选择

```typescript
// 获取单个元素
const element = query('.my-element')

// 获取多个元素
const elements = queryAll('.my-elements')
```

### 类名操作

```typescript
// 批量添加类名
addClass('.buttons', 'btn-primary')

// 批量移除类名
removeClass('.buttons', 'btn-primary')

// 批量切换类名
toggleClass('.buttons', 'active')
```

### 激活状态管理

这是针对你提到的场景的核心功能：

```typescript
// 原来的代码:
colorTitles.forEach((title) => title.classList.remove('active'))
colorTexts.forEach((text) => text.classList.remove('active'))
colorTitles[curIdx].classList.add('active')
colorTexts[curIdx].classList.add('active')

// 使用工具库 - 方式1:
setActiveByIndex('.color-title', 'active', curIdx)
setActiveByIndex('.color-text', 'active', curIdx)

// 使用工具库 - 方式2 (推荐):
setMultipleActiveByIndex([
  { elements: '.color-title', className: 'active' },
  { elements: '.color-text', className: 'active' }
], curIdx)
```

### 样式操作

```typescript
// 批量设置样式
setStyle('.cards', {
  backgroundColor: '#f0f0f0',
  padding: '20px',
  borderRadius: '8px'
})
```

### 属性操作

```typescript
// 批量设置属性
setAttr('.links', {
  'data-track': 'true',
  'target': '_blank'
})

// 批量移除属性
removeAttr('.forms', 'disabled', 'readonly')
```

### 内容操作

```typescript
// 批量设置文本
setText('.counters', '0')

// 批量设置HTML
setHtml('.containers', '<div class="content">新内容</div>')
```

### 事件处理

```typescript
// 批量添加事件监听器
on('.buttons', 'click', (e) => {
  console.log('按钮被点击:', e.target)
})

// 批量移除事件监听器
off('.buttons', 'click', handler)
```

### 显示/隐藏

```typescript
// 显示元素
show('.hidden-elements')

// 隐藏元素
hide('.visible-elements')
```

## 实际应用场景

### 标签页切换

```typescript
function setupTabs() {
  on('.tab-button', 'click', (e) => {
    const clickedButton = e.target as HTMLElement
    const tabIndex = Array.from(queryAll('.tab-button')).indexOf(clickedButton)

    setMultipleActiveByIndex([
      { elements: '.tab-button', className: 'active' },
      { elements: '.tab-content', className: 'active' }
    ], tabIndex)
  })
}
```

### 轮播图控制

```typescript
function goToSlide(index: number) {
  setMultipleActiveByIndex([
    { elements: '.slide', className: 'active' },
    { elements: '.indicator', className: 'active' }
  ], index)
}
```

### 手风琴组件

```typescript
function setupAccordion() {
  on('.accordion-header', 'click', (e) => {
    const header = e.target as Element
    const accordion = header.closest('.accordion-item') as Element

    // 切换当前项
    toggleClass([accordion], 'expanded')

    // 关闭其他项
    const allItems = queryAll('.accordion-item')
    Array.from(allItems).forEach(item => {
      if (item !== accordion) {
        removeClass([item], 'expanded')
      }
    })
  })
}
```

## 类型支持

所有函数都提供完整的TypeScript类型支持，包括：

- 元素选择器字符串
- HTMLElement数组
- NodeListOf<HTMLElement>
- 事件类型推断
- CSS样式属性类型检查

## 优势

1. **简洁性**: 减少重复的forEach循环代码
2. **一致性**: 统一的API设计
3. **类型安全**: 完整的TypeScript支持
4. **灵活性**: 支持选择器字符串和元素数组
5. **性能**: 内部优化的批量操作

## 兼容性

支持所有现代浏览器，包括：
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
